{"meta": {"language": "zh-CN", "displayName": "简体中文", "author": "Minidoracat", "version": "1.0.0", "lastUpdate": "2025-01-31"}, "app": {"title": "交互式反馈收集", "projectDirectory": "项目目录", "language": "语言", "settings": "设置", "confirmCancel": "确认取消", "confirmCancelMessage": "确定要取消反馈吗？所有输入的内容将会丢失。", "layoutChangeTitle": "界面布局变更", "layoutChangeMessage": "布局模式已变更，需要重新加载界面才能生效。\n是否现在重新加载？"}, "tabs": {"summary": "📋 AI 摘要", "feedback": "💬 反馈", "command": "⚡ 命令", "language": "⚙️ 设置", "images": "🖼️ 图片", "about": "ℹ️ 关于"}, "feedback": {"title": "您的反馈", "description": "请描述您对 AI 工作结果的想法、建议或需要修改的地方。", "placeholder": "请在这里输入您的反馈、建议或问题...\n\n💡 小提示：\n• 按 Ctrl+Enter（支持数字键盘）可快速提交反馈\n• 按 Ctrl+V 可直接粘贴剪贴板图片", "emptyTitle": "反馈内容为空", "emptyMessage": "请先输入反馈内容再提交。您可以描述想法、建议或需要修改的地方。"}, "summary": {"title": "AI 工作摘要", "description": "以下是 AI 刚才为您完成的工作内容，请检视并提供反馈。", "testDescription": "以下是 AI 回复的消息内容，请检视并提供反馈。"}, "command": {"title": "命令执行", "description": "您可以执行命令来验证结果或收集更多信息。", "placeholder": "输入要执行的命令...", "output": "命令输出", "outputPlaceholder": "命令输出将在这里显示...", "run": "▶️ 执行", "terminate": "⏹️ 停止"}, "images": {"title": "🖼️ 图片附件（可选）", "select": "选择文件", "paste": "剪贴板", "clear": "清除", "status": "已选择 {count} 张图片", "statusWithSize": "已选择 {count} 张图片 (总计 {size})", "dragHint": "🎯 拖拽图片到这里 或 按 Ctrl+V/Cmd+V 粘贴剪贴板图片 (PNG、JPG、JPEG、GIF、BMP、WebP)", "deleteConfirm": "确定要移除图片 \"{filename}\" 吗？", "deleteTitle": "确认删除", "sizeWarning": "图片文件大小不能超过 1MB", "formatError": "不支持的图片格式", "paste_images": "📋 从剪贴板粘贴", "paste_failed": "粘贴失败，剪贴板中没有图片", "paste_no_image": "剪贴板中没有图片可粘贴", "paste_image_from_textarea": "已将图片从文本框智能贴到图片区域", "images_clear": "清除所有图片", "settings": {"title": "图片设置", "sizeLimit": "图片大小限制", "sizeLimitOptions": {"unlimited": "无限制", "1mb": "1MB", "3mb": "3MB", "5mb": "5MB"}, "base64Detail": "Base64 兼容模式", "base64DetailHelp": "启用后会在文本中包含完整的 Base64 图片数据，提升与 Gemini 等 AI 模型的兼容性", "base64Warning": "⚠️ 会增加传输量", "compatibilityHint": "💡 图片无法正确识别？", "enableBase64Hint": "尝试启用 Base64 兼容模式"}, "sizeLimitExceeded": "图片 {filename} 大小为 {size}，超过 {limit} 限制！", "sizeLimitExceededAdvice": "建议使用图片编辑软件压缩后再上传，或调整图片大小限制设置。"}, "settings": {"title": "应用设置", "language": {"title": "语言设置", "selector": "🌐 语言选择"}, "layout": {"title": "界面布局", "separateMode": "分离模式", "separateModeDescription": "AI 摘要和反馈分别在不同页签", "combinedVertical": "合并模式（垂直布局）", "combinedVerticalDescription": "AI 摘要在上，反馈输入在下，摘要和反馈在同一页面", "combinedHorizontal": "合并模式（水平布局）", "combinedHorizontalDescription": "AI 摘要在左，反馈输入在右，增大摘要可视区域"}, "window": {"title": "窗口定位", "alwaysCenter": "总是在主屏幕中心显示窗口"}, "reset": {"title": "重置设置", "button": "重置设置", "confirmTitle": "确认重置设置", "confirmMessage": "确定要重置所有设置吗？这将清除所有已保存的偏好设置并恢复到默认状态。", "successTitle": "重置成功", "successMessage": "所有设置已成功重置为默认值。", "errorTitle": "重置失败", "errorMessage": "重置设置时发生错误：{error}"}}, "buttons": {"submit": "提交反馈", "cancel": "取消", "close": "关闭", "clear": "清除", "submitFeedback": "✅ 提交反馈", "selectFiles": "📁 选择文件", "pasteClipboard": "📋 剪贴板", "clearAll": "✕ 清除", "runCommand": "▶️ 执行"}, "status": {"feedbackSubmitted": "反馈已成功提交！", "feedbackCancelled": "已取消反馈。", "timeoutMessage": "等待反馈超时", "errorOccurred": "发生错误", "loading": "加载中...", "connecting": "连接中...", "connected": "已连接", "disconnected": "连接中断", "uploading": "上传中...", "uploadSuccess": "上传成功", "uploadFailed": "上传失败", "commandRunning": "命令执行中...", "commandFinished": "命令执行完成", "pasteSuccess": "已从剪贴板粘贴图片", "pasteFailed": "无法从剪贴板获取图片", "invalidFileType": "不支持的文件类型", "fileTooLarge": "文件过大（最大 1MB）"}, "errors": {"title": "错误", "warning": "警告", "info": "提示", "interfaceReloadError": "重新加载界面时发生错误: {error}", "imageSaveEmpty": "保存的图片文件为空！位置: {path}", "imageSaveFailed": "图片保存失败！", "clipboardSaveFailed": "无法保存剪贴板图片！", "noValidImage": "剪贴板中没有有效的图片！", "noImageContent": "剪贴板中没有图片内容！", "emptyFile": "图片 {filename} 是空文件！", "loadImageFailed": "无法加载图片 {filename}:\n{error}", "dragInvalidFiles": "请拖拽有效的图片文件！", "confirmClearAll": "确定要清除所有 {count} 张图片吗？", "confirmClearTitle": "确认清除", "fileSizeExceeded": "图片 {filename} 大小为 {size}MB，超过 1MB 限制！\n建议使用图片编辑软件压缩后再上传。", "dataSizeExceeded": "图片 {filename} 数据大小超过 1MB 限制！"}, "aiSummary": "AI 工作摘要", "languageSelector": "🌐 语言选择", "languageNames": {"zhTw": "繁體中文", "en": "English", "zhCn": "简体中文"}, "test": {"qtGuiSummary": "🎯 图片预览和窗口调整测试\n\n这是一个测试会话，用于验证以下功能：\n\n✅ 功能测试项目：\n1. 图片上传和预览功能\n2. 图片右上角X删除按钮\n3. 窗口自由调整大小\n4. 分割器的灵活调整\n5. 各区域的动态布局\n6. 智能 Ctrl+V 图片粘贴功能\n\n📋 测试步骤：\n1. 尝试上传一些图片（拖拽、文件选择、剪贴板）\n2. 检查图片预览是否正常显示\n3. 点击图片右上角的X按钮删除图片\n4. 尝试调整窗口大小，检查是否可以自由调整\n5. 拖动分割器调整各区域大小\n6. 在文本框内按 Ctrl+V 测试智能粘贴功能\n7. 提供任何回馈或发现的问题\n\n请测试这些功能并提供回馈！", "webUiSummary": "测试 Web UI 功能\n\n🎯 **功能测试项目：**\n- Web UI 服务器启动和运行\n- WebSocket 即时通讯\n- 回馈提交功能\n- 图片上传和预览\n- 命令执行功能\n- 智能 Ctrl+V 图片粘贴\n- 多语言界面切换\n\n📋 **测试步骤：**\n1. 测试图片上传（拖拽、选择文件、剪贴板）\n2. 在文本框内按 Ctrl+V 测试智能粘贴\n3. 尝试切换语言（繁中/简中/英文）\n4. 测试命令执行功能\n5. 提交回馈和图片\n\n请测试这些功能并提供回馈！"}, "about": {"appInfo": "应用程序信息", "version": "版本", "description": "一个强大的 MCP 服务器，为 AI 辅助开发工具提供人在回路的交互反馈功能。支持 Qt GUI 和 Web UI 双界面，并具备图片上传、命令执行、多语言等丰富功能。", "projectLinks": "项目链接", "githubProject": "GitHub 项目", "visitGithub": "访问 GitHub", "contact": "联系与支持", "discordSupport": "Discord 支持", "joinDiscord": "加入 Discord", "contactDescription": "如需技术支持、问题反馈或功能建议，欢迎通过 Discord 社群或 GitHub Issues 与我们联系。", "thanks": "致谢与贡献", "thanksText": "感谢原作者 <PERSON><PERSON><PERSON> (@fabiomlferreira) 创建了原始的 interactive-feedback-mcp 项目。\n\n本增强版本由 Minidoracat 开发和维护，大幅扩展了项目功能，新增了 GUI 界面、图片支持、多语言能力以及许多其他改进功能。\n\n同时感谢 sanshao85 的 mcp-feedback-collector 项目提供的 UI 设计灵感。\n\n开源协作让技术变得更美好！"}}